ultrathink

/Users/<USER>/Developer/my/auto_test_sms/sms-testing-tool/app/page.tsx 文件太大了，请你优化，要求页面所有的样式和功能、组件都不变，只改变代码，不改变页面。

The file `app/page.tsx` has become too large and needs to be refactored for better maintainability. Please optimize this file by extracting reusable logic and components while maintaining the exact same functionality and UI appearance.

Requirements:
1. **Preserve all functionality**: Every feature must work exactly as before (SMS sending, template selection, phone number management, status monitoring, bulk sending, etc.)
2. **Maintain identical UI/UX**: No visual changes to the page layout, styling, or user interactions
3. **Extract custom hooks**: Move complex state management logic (like token management, SMS status monitoring, phone number search) into separate custom hooks in the `hooks/` directory
4. **Create utility functions**: Extract helper functions (like `getStatusBadgeVariant`, `getErrorMessage`, API calls) into the `lib/` directory
5. **Component extraction**: Break down large JSX sections into smaller, focused components in the `components/` directory while keeping them co-located if they're only used by this page
6. **Maintain existing imports**: Keep all current dependencies and ensure no breaking changes to the component's external interface
7. **Preserve state management**: All useState, useEffect, and localStorage logic should continue working identically
8. **Keep TypeScript types**: Maintain all existing interfaces and type definitions

The goal is to improve code organization and readability without changing any user-facing behavior or appearance.

ultrathink
在 ‘http://localhost:3030/analytics’ 页面，‘失败原因分析’ 模块，failureReasons，概览标签中，errorCode作为副标题，中文名称作为主标题；
‘按运营商’ 和‘按模版’标签中，不要展示 ‘还有 2 种其他失败原因...’ 展示全部的原因，然后不要展示 errorCode ，要展示中文名。

我现在正在测试软件，我发现一个问题，复现步骤如下，请你描述给开发人员：
1. 首先我注册一个账号，认证一家公司，我是这家公司的管理员；
2. 然后我邀请一个员工A，员工加入该公司；
3. 将管理员移交给这个员工A
4. 在员工管理中，我点击退出这个公司
5. 问题：使用员工A登录供应商网页端，提示用户不存在；我使用账号重新登录，提示企业需要重新认证；

我作为认证公司的用户，默认是管理员角色，后来将管理员移交给用户A，然后我点击退出公司，提示公司需要重新认证（这是对的），但是使用其他用户账号登录，提示用户不存在。

反馈一个测试中发现的问题，复现步骤及现象如下：

1. 我注册账号后，认证了一家公司，默认作为该公司管理员；
2. 邀请员工A加入公司，员工A成功加入；
3. 将管理员权限移交给员工A；
4. 我在员工管理中退出该公司；
5. 出现异常：
   - 用员工A的账号登录供应商网页端，提示“用户不存在”；
   - 我用自己的账号重新登录，提示“企业需要重新认证”（这个符合预期）。

问题核心：管理员移交后原管理员退出公司，接收管理员权限的员工A登录时被提示用户不存在，不符合正常逻辑，麻烦排查下~

ultrathink
在 ‘http://localhost:3030/monitor’ 页面，如果重发次数为 0 ，不要在列表中展示0 ，我看到是这个模块中的 ‘document.querySelector("body > div.min-h-screen.bg-gray-50.p-4 > div > div:nth-child(5) > div.p-6.pt-0 > div.space-y-3 > div:nth-child(2) > div.text-sm.text-gray-600.space-y-1")’

ultrathink
http://localhost:3030/analytics 页面中的
日发送趋势 组件中的进度条没有按照实际数据内容正确展示