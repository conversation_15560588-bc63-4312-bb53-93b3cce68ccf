# 公司数据导入功能说明

## 功能概述

公司数据导入功能已经完全重新设计，实现了从Excel文件到数据库的自动化导入流程，替代了之前复杂的手动操作步骤。

## 主要特性

### 1. 自动化导入流程
- **一键导入**：上传Excel文件后自动处理并更新数据库
- **双表更新**：同时更新`seller_company`和`seller_company_lang`表
- **事务处理**：确保数据一致性，失败时自动回滚

### 2. 数据验证
- **格式验证**：自动验证公司ID、手机号、邮箱、网站地址等格式
- **必填字段检查**：确保关键字段不为空
- **实时反馈**：显示验证错误的具体位置和原因

### 3. 用户友好界面
- **数据预览**：导入前可预览所有数据
- **进度显示**：实时显示处理进度
- **结果统计**：显示成功/失败数量和详细错误信息

## 数据库结构

### seller_company 表（中文信息）
```sql
- company_id: 公司ID（主键）
- name: 中文公司名称
- province: 省份
- city: 城市
- contact_person: 联系人
- mobile: 手机号
- email: 邮箱
- ... 其他字段
```

### seller_company_lang 表（多语言信息）
```sql
- company_id: 公司ID（外键）
- language_code: 语言代码（如 en-US）
- name: 英文公司名称
- province: 英文省份
- city: 英文城市
- ... 其他英文字段
```

## Excel模板格式

模板包含以下字段（按顺序）：
1. company_id - 公司ID
2. company_no - 公司编号
3. name - 中文名称
4. name_en - 英文名称
5. country - 国家
6. province - 省份
7. province_en - 英文省份
8. city - 城市
9. city_en - 英文城市
10. county - 区县
11. county_en - 英文区县
12. address - 地址
13. address_en - 英文地址
14. business_scope - 经营范围
15. business_scope_en - 英文经营范围
16. contact_person - 联系人
17. contact_person_en - 英文联系人
18. contact_person_title - 联系人职位
19. contact_person_title_en - 英文联系人职位
20. mobile - 手机号
21. phone - 电话
22. email - 邮箱
23. intro - 公司介绍
24. intro_en - 英文公司介绍
25. whats_app - WhatsApp
26. fax - 传真
27. postal_code - 邮编
28. company_birth - 成立时间
29. is_verified - 验证状态（1=已验证，0=未验证）
30. homepage - 网站地址

## 使用步骤

### 1. 下载模板
- 点击"下载模板"按钮获取标准Excel模板
- 模板包含示例数据，可参考格式填写

### 2. 准备数据
- 按照模板格式填写公司数据
- 确保company_id为有效的正整数
- 中英文名称不能为空
- 手机号、邮箱、网站地址需符合格式要求

### 3. 上传文件
- 选择准备好的Excel文件
- 系统自动解析并验证数据
- 查看数据预览和验证结果

### 4. 确认导入
- 修复所有验证错误（如有）
- 点击"确认导入"开始数据库更新
- 查看导入结果统计

## 技术实现

### 前端功能
- 使用`xlsx`库解析Excel文件
- React状态管理处理导入流程
- 实时数据验证和错误显示
- 响应式UI设计

### 后端API
- `/api/supplier-import` 处理数据导入
- 使用SQLite数据库存储
- 事务处理确保数据一致性
- 详细的错误处理和日志

### 数据处理逻辑
1. 解析Excel文件获取原始数据
2. 验证数据格式和必填字段
3. 使用事务批量更新数据库
4. 同时更新中文表和英文表
5. 返回详细的处理结果

## 优势对比

### 之前的手动流程
1. 删除Excel表头
2. 使用Navicat导入向导
3. 创建临时表
4. 手动执行多条SQL更新语句
5. 验证数据并删除临时表

### 现在的自动化流程
1. 下载模板并填写数据
2. 上传Excel文件
3. 预览和验证数据
4. 一键完成导入

**节省时间：从15-20分钟缩短到2-3分钟**
**降低错误：自动验证减少人为错误**
**提升体验：可视化界面更加友好**

## 注意事项

1. **数据备份**：导入前建议备份现有数据
2. **格式要求**：严格按照模板格式准备数据
3. **网络连接**：确保网络稳定，避免导入中断
4. **数据量限制**：建议单次导入不超过1000条记录
5. **权限要求**：需要数据库写入权限

## 故障排除

### 常见问题
1. **文件格式错误**：确保使用Excel格式，不是CSV
2. **字段顺序错误**：严格按照模板字段顺序
3. **数据格式错误**：检查手机号、邮箱、网站地址格式
4. **网络超时**：重新尝试导入或减少数据量

### 错误代码
- `400`：数据格式错误
- `500`：服务器内部错误
- 具体错误信息会在界面上显示

这个新的导入功能大大简化了公司数据的管理流程，提高了工作效率和数据准确性。
