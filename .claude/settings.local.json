{"permissions": {"allow": ["Bash(git init:*)", "Bash(git remote add:*)", "Bash(git add:*)", "Bash(git commit:*)", "Bash(git push:*)", "Bash(rm:*)", "Bash(npm install:*)", "Bash(npm run build:*)", "Bash(npm run lint)", "Bash(npx tsc:*)", "Bash(npm run dev:*)", "Bash(grep:*)", "<PERSON><PERSON>(mkdir:*)", "Bash(lsof:*)", "<PERSON><PERSON>(curl:*)", "Bash(sqlite3:*)", "Bash(ls:*)", "Bash(find:*)", "Bash(kill:*)", "Bash(git rebase:*)", "Bash(git filter-branch:*)", "Bash(git reset:*)", "<PERSON><PERSON>(python3:*)", "<PERSON><PERSON>(chmod:*)", "Bash(git branch:*)", "Bash(git filter-repo:*)", "Bash(ls:*)", "<PERSON><PERSON>(git rev-list:*)", "Bash(git fetch:*)", "Bash(npx @sentry/wizard:*)", "<PERSON><PERSON>(pkill:*)", "<PERSON><PERSON>(mv:*)", "Bash(EDITOR=true git rebase -i HEAD~2)", "mcp__ide__getDiagnostics", "<PERSON><PERSON>(time curl:*)", "<PERSON><PERSON>(git clean:*)", "<PERSON><PERSON>(mv:*)"], "deny": []}}