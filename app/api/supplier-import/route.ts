import { NextRequest, NextResponse } from 'next/server'
import Database from 'better-sqlite3'
import path from 'path'

interface CompanyData {
  company_id: number
  company_no?: string
  name: string
  name_en: string
  country?: string
  province?: string
  province_en?: string
  city?: string
  city_en?: string
  county?: string
  county_en?: string
  address?: string
  address_en?: string
  business_scope?: string
  business_scope_en?: string
  contact_person?: string
  contact_person_en?: string
  contact_person_title?: string
  contact_person_title_en?: string
  mobile?: string
  phone?: string
  email?: string
  intro?: string
  intro_en?: string
  whats_app?: string
  fax?: string
  postal_code?: string
  company_birth?: string
  is_verified?: number
  homepage?: string
}

interface ImportResult {
  totalProcessed: number
  successCount: number
  errorCount: number
  errors: string[]
}

// 初始化数据库连接
function getDatabase() {
  const dbPath = path.join(process.cwd(), 'company.db')
  const db = new Database(dbPath)
  
  // 创建表（如果不存在）
  db.exec(`
    CREATE TABLE IF NOT EXISTS seller_company (
      id INTEGER PRIMARY KEY AUTOINCREMENT,
      company_id INTEGER UNIQUE,
      company_no TEXT,
      name TEXT,
      country TEXT,
      province TEXT,
      city TEXT,
      county TEXT,
      address TEXT,
      business_scope TEXT,
      contact_person TEXT,
      contact_person_title TEXT,
      mobile TEXT,
      phone TEXT,
      email TEXT,
      intro TEXT,
      whats_app TEXT,
      fax TEXT,
      postal_code TEXT,
      company_birth TEXT,
      is_verified INTEGER DEFAULT 0,
      homepage TEXT,
      created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
      updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
    )
  `)
  
  db.exec(`
    CREATE TABLE IF NOT EXISTS seller_company_lang (
      id INTEGER PRIMARY KEY AUTOINCREMENT,
      company_id INTEGER,
      language_code TEXT,
      name TEXT,
      province TEXT,
      city TEXT,
      county TEXT,
      address TEXT,
      business_scope TEXT,
      contact_person TEXT,
      contact_person_title TEXT,
      intro TEXT,
      created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
      updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
      FOREIGN KEY (company_id) REFERENCES seller_company (company_id)
    )
  `)
  
  return db
}

export async function POST(request: NextRequest) {
  try {
    const { companies } = await request.json()
    
    if (!companies || !Array.isArray(companies)) {
      return NextResponse.json(
        { error: '无效的数据格式' },
        { status: 400 }
      )
    }

    const db = getDatabase()
    const result: ImportResult = {
      totalProcessed: companies.length,
      successCount: 0,
      errorCount: 0,
      errors: []
    }

    // 开始事务
    const transaction = db.transaction((companies: CompanyData[]) => {
      for (const company of companies) {
        try {
          // 更新或插入seller_company表
          const updateCompanyStmt = db.prepare(`
            INSERT OR REPLACE INTO seller_company (
              company_id, company_no, name, country, province, city, county, 
              address, business_scope, contact_person, contact_person_title, 
              mobile, phone, email, intro, whats_app, fax, postal_code, 
              company_birth, is_verified, homepage, updated_at
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, CURRENT_TIMESTAMP)
          `)
          
          updateCompanyStmt.run(
            company.company_id,
            company.company_no || null,
            company.name || null,
            company.country || null,
            company.province || null,
            company.city || null,
            company.county || null,
            company.address || null,
            company.business_scope || null,
            company.contact_person || null,
            company.contact_person_title || null,
            company.mobile || null,
            company.phone || null,
            company.email || null,
            company.intro || null,
            company.whats_app || null,
            company.fax || null,
            company.postal_code || null,
            company.company_birth || null,
            company.is_verified || 0,
            company.homepage || null
          )

          // 更新或插入seller_company_lang表（英文）
          const updateLangStmt = db.prepare(`
            INSERT OR REPLACE INTO seller_company_lang (
              company_id, language_code, name, province, city, county, 
              address, business_scope, contact_person, contact_person_title, 
              intro, updated_at
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, CURRENT_TIMESTAMP)
          `)
          
          updateLangStmt.run(
            company.company_id,
            'en-US',
            company.name_en || null,
            company.province_en || null,
            company.city_en || null,
            company.county_en || null,
            company.address_en || null,
            company.business_scope_en || null,
            company.contact_person_en || null,
            company.contact_person_title_en || null,
            company.intro_en || null
          )

          result.successCount++
        } catch (error) {
          result.errorCount++
          result.errors.push(`公司ID ${company.company_id}: ${error instanceof Error ? error.message : '未知错误'}`)
        }
      }
    })

    // 执行事务
    transaction(companies)
    db.close()

    return NextResponse.json(result)
  } catch (error) {
    console.error('导入失败:', error)
    return NextResponse.json(
      { error: error instanceof Error ? error.message : '导入失败' },
      { status: 500 }
    )
  }
}

export async function GET() {
  return NextResponse.json({ message: '供应商导入API' })
}
