"use client"

import React, { useState, useRef } from "react"
import { 
  Upload, 
  FileText, 
  CheckCircle, 
  AlertCircle, 
  Download,
  Trash2,
  Eye,
  Save
} from "lucide-react"
import { But<PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Alert, AlertDescription } from "@/components/ui/alert"
import { Badge } from "@/components/ui/badge"
import { Progress } from "@/components/ui/progress"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { useToast } from "@/hooks/use-toast"
import * as XLSX from 'xlsx'

interface CompanyData {
  company_id: number
  company_no?: string
  name: string
  name_en: string
  country?: string
  province?: string
  province_en?: string
  city?: string
  city_en?: string
  county?: string
  county_en?: string
  address?: string
  address_en?: string
  business_scope?: string
  business_scope_en?: string
  contact_person?: string
  contact_person_en?: string
  contact_person_title?: string
  contact_person_title_en?: string
  mobile?: string
  phone?: string
  email?: string
  intro?: string
  intro_en?: string
  whats_app?: string
  fax?: string
  postal_code?: string
  company_birth?: string
  is_verified?: number
  homepage?: string
}

interface ValidationError {
  row: number
  field: string
  message: string
}

export default function SupplierImportPage() {
  const { toast } = useToast()
  const fileInputRef = useRef<HTMLInputElement>(null)

  const [file, setFile] = useState<File | null>(null)
  const [companyData, setCompanyData] = useState<CompanyData[]>([])
  const [validationErrors, setValidationErrors] = useState<ValidationError[]>([])
  const [isProcessing, setIsProcessing] = useState(false)
  const [importProgress, setImportProgress] = useState(0)
  const [importStatus, setImportStatus] = useState<'idle' | 'processing' | 'completed' | 'error'>('idle')
  const [importResults, setImportResults] = useState<{
    totalProcessed: number
    successCount: number
    errorCount: number
    errors: string[]
  } | null>(null)

  const handleFileSelect = (event: React.ChangeEvent<HTMLInputElement>) => {
    const selectedFile = event.target.files?.[0]
    if (selectedFile) {
      setFile(selectedFile)
      processFile(selectedFile)
    }
  }

  const processFile = async (file: File) => {
    setIsProcessing(true)
    setImportProgress(0)

    try {
      const data = await file.arrayBuffer()
      const workbook = XLSX.read(data, { type: 'array' })
      const sheetName = workbook.SheetNames[0]
      const worksheet = workbook.Sheets[sheetName]
      const jsonData = XLSX.utils.sheet_to_json(worksheet, { header: 1 }) as any[][]

      if (jsonData.length < 2) {
        throw new Error('文件必须包含标题行和至少一行数据')
      }

      // 第一行是标题，从第二行开始是数据
      const headers = jsonData[0]
      const rows = jsonData.slice(1).filter(row => row.some(cell => cell !== null && cell !== undefined && cell !== ''))

      // 映射数据到CompanyData格式
      const mappedData: CompanyData[] = rows.map((row, index) => ({
        company_id: parseInt(row[0]) || 0,
        company_no: row[1] || '',
        name: row[2] || '',
        name_en: row[3] || '',
        country: row[4] || '',
        province: row[5] || '',
        province_en: row[6] || '',
        city: row[7] || '',
        city_en: row[8] || '',
        county: row[9] || '',
        county_en: row[10] || '',
        address: row[11] || '',
        address_en: row[12] || '',
        business_scope: row[13] || '',
        business_scope_en: row[14] || '',
        contact_person: row[15] || '',
        contact_person_en: row[16] || '',
        contact_person_title: row[17] || '',
        contact_person_title_en: row[18] || '',
        mobile: row[19] || '',
        phone: row[20] || '',
        email: row[21] || '',
        intro: row[22] || '',
        intro_en: row[23] || '',
        whats_app: row[24] || '',
        fax: row[25] || '',
        postal_code: row[26] || '',
        company_birth: row[27] || '',
        is_verified: parseInt(row[28]) || 0,
        homepage: row[29] || ''
      }))

      // 验证数据
      const errors = validateCompanyData(mappedData)
      setValidationErrors(errors)
      setCompanyData(mappedData)
      setImportProgress(100)

      if (errors.length === 0) {
        setImportStatus('completed')
        toast({
          title: "文件处理成功",
          description: `成功处理 ${mappedData.length} 条公司记录`,
        })
      } else {
        setImportStatus('error')
        toast({
          title: "数据验证失败",
          description: `发现 ${errors.length} 个验证错误`,
          variant: "destructive",
        })
      }
    } catch (error) {
      setImportStatus('error')
      toast({
        title: "文件处理失败",
        description: error instanceof Error ? error.message : "未知错误",
        variant: "destructive",
      })
    } finally {
      setIsProcessing(false)
    }
  }

  const validateCompanyData = (data: CompanyData[]): ValidationError[] => {
    const errors: ValidationError[] = []

    data.forEach((company, index) => {
      const rowNumber = index + 2 // +2 because of header row and 0-based index

      if (!company.company_id || company.company_id <= 0) {
        errors.push({ row: rowNumber, field: 'company_id', message: '公司ID必须是有效的正整数' })
      }

      if (!company.name.trim()) {
        errors.push({ row: rowNumber, field: 'name', message: '公司中文名称不能为空' })
      }

      if (!company.name_en.trim()) {
        errors.push({ row: rowNumber, field: 'name_en', message: '公司英文名称不能为空' })
      }

      if (company.email && !/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(company.email)) {
        errors.push({ row: rowNumber, field: 'email', message: '邮箱格式不正确' })
      }

      if (company.mobile && !/^1[3-9]\d{9}$/.test(company.mobile.replace(/\s|-/g, ''))) {
        errors.push({ row: rowNumber, field: 'mobile', message: '手机号格式不正确' })
      }

      if (company.homepage && company.homepage.trim() && !/^https?:\/\/.+/.test(company.homepage)) {
        errors.push({ row: rowNumber, field: 'homepage', message: '网站地址格式不正确，应以http://或https://开头' })
      }
    })

    return errors
  }

  const handleImport = async () => {
    if (validationErrors.length > 0) {
      toast({
        title: "无法导入",
        description: "请先修复所有验证错误",
        variant: "destructive",
      })
      return
    }

    setIsProcessing(true)
    setImportProgress(0)
    setImportResults(null)

    try {
      // 调用后端API进行数据导入
      const response = await fetch('/api/supplier-import', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          companies: companyData
        })
      })

      if (!response.ok) {
        const errorData = await response.json()
        throw new Error(errorData.error || '导入失败')
      }

      const result = await response.json()
      setImportResults(result)
      setImportProgress(100)

      toast({
        title: "导入完成",
        description: `成功处理 ${result.totalProcessed} 条记录，成功 ${result.successCount} 条，失败 ${result.errorCount} 条`,
      })

      // 清理状态
      setFile(null)
      setCompanyData([])
      setValidationErrors([])
      setImportStatus('idle')
      if (fileInputRef.current) {
        fileInputRef.current.value = ''
      }
    } catch (error) {
      toast({
        title: "导入失败",
        description: error instanceof Error ? error.message : "未知错误",
        variant: "destructive",
      })
      setImportStatus('error')
    } finally {
      setIsProcessing(false)
    }
  }

  const downloadTemplate = () => {
    const templateData = [
      [
        'company_id', 'company_no', 'name', 'name_en', 'country', 'province', 'province_en',
        'city', 'city_en', 'county', 'county_en', 'address', 'address_en', 'business_scope',
        'business_scope_en', 'contact_person', 'contact_person_en', 'contact_person_title',
        'contact_person_title_en', 'mobile', 'phone', 'email', 'intro', 'intro_en',
        'whats_app', 'fax', 'postal_code', 'company_birth', 'is_verified', 'homepage'
      ],
      [
        3, '', '重庆市众力生物工程有限公司', 'Chongqing Zhongli Bioengineering Co.Ltd', 'CN',
        '重庆市', 'Chongqing', '重庆市', 'Chongqing', '綦江区', 'Qijiang District',
        '重庆市綦江区古南街道金福大道34号', 'No. 34, Jinfu Avenue, Gunan Street, Qijiang District, Chongqing',
        '', '', '覃云菊', '', '法人', 'legal representative', '***********',
        '023-67610806/66956993', '<EMAIL>', '', '', '', '023-67611298',
        '400020', '', 1, ''
      ]
    ]

    const ws = XLSX.utils.aoa_to_sheet(templateData)
    const wb = XLSX.utils.book_new()
    XLSX.utils.book_append_sheet(wb, ws, '公司数据模板')
    XLSX.writeFile(wb, '公司数据导入模板.xlsx')
  }

  const clearData = () => {
    setFile(null)
    setCompanyData([])
    setValidationErrors([])
    setImportStatus('idle')
    setImportProgress(0)
    setImportResults(null)
    if (fileInputRef.current) {
      fileInputRef.current.value = ''
    }
  }

  return (
    <div className="container mx-auto px-4 py-8">
      <div className="max-w-6xl mx-auto space-y-6">
        {/* Header */}
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold text-gray-900">公司数据导入</h1>
            <p className="text-muted-foreground mt-1">导入和管理公司数据，支持中英文双语信息自动更新</p>
          </div>
          <div className="flex items-center gap-2">
            <Button variant="outline" onClick={downloadTemplate}>
              <Download className="w-4 h-4 mr-2" />
              下载模板
            </Button>
          </div>
        </div>

        {/* File Upload Section */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Upload className="h-5 w-5" />
              文件上传
            </CardTitle>
            <CardDescription>
              支持 Excel (.xlsx, .xls) 和 CSV 文件格式。请使用标准模板格式，确保字段顺序正确。
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div className="flex items-center gap-4">
                <input
                  ref={fileInputRef}
                  type="file"
                  accept=".xlsx,.xls,.csv"
                  onChange={handleFileSelect}
                  className="hidden"
                />
                <Button 
                  onClick={() => fileInputRef.current?.click()}
                  disabled={isProcessing}
                >
                  <Upload className="w-4 h-4 mr-2" />
                  选择文件
                </Button>
                {file && (
                  <div className="flex items-center gap-2">
                    <FileText className="h-4 w-4 text-muted-foreground" />
                    <span className="text-sm">{file.name}</span>
                    <Button variant="ghost" size="sm" onClick={clearData}>
                      <Trash2 className="h-3 w-3" />
                    </Button>
                  </div>
                )}
              </div>
              
              {isProcessing && (
                <div className="space-y-2">
                  <div className="flex items-center justify-between text-sm">
                    <span>处理进度</span>
                    <span>{importProgress}%</span>
                  </div>
                  <Progress value={importProgress} />
                </div>
              )}
            </div>
          </CardContent>
        </Card>

        {/* Status and Results */}
        {companyData.length > 0 && (
          <Tabs defaultValue="preview" className="space-y-4">
            <TabsList>
              <TabsTrigger value="preview" className="flex items-center gap-2">
                <Eye className="h-4 w-4" />
                数据预览 ({companyData.length})
              </TabsTrigger>
              {validationErrors.length > 0 && (
                <TabsTrigger value="errors" className="flex items-center gap-2">
                  <AlertCircle className="h-4 w-4" />
                  验证错误 ({validationErrors.length})
                </TabsTrigger>
              )}
              {importResults && (
                <TabsTrigger value="results" className="flex items-center gap-2">
                  <CheckCircle className="h-4 w-4" />
                  导入结果
                </TabsTrigger>
              )}
            </TabsList>

            <TabsContent value="preview">
              <Card>
                <CardHeader className="flex flex-row items-center justify-between">
                  <div>
                    <CardTitle>数据预览</CardTitle>
                    <CardDescription>
                      预览即将导入的公司数据
                    </CardDescription>
                  </div>
                  <div className="flex items-center gap-2">
                    <Badge variant={validationErrors.length === 0 ? "default" : "destructive"}>
                      {validationErrors.length === 0 ? (
                        <><CheckCircle className="h-3 w-3 mr-1" />验证通过</>
                      ) : (
                        <><AlertCircle className="h-3 w-3 mr-1" />{validationErrors.length} 个错误</>
                      )}
                    </Badge>
                    <Button
                      onClick={handleImport}
                      disabled={validationErrors.length > 0 || isProcessing}
                    >
                      <Save className="w-4 h-4 mr-2" />
                      确认导入
                    </Button>
                  </div>
                </CardHeader>
                <CardContent>
                  <div className="rounded-md border overflow-x-auto">
                    <Table>
                      <TableHeader>
                        <TableRow>
                          <TableHead>公司ID</TableHead>
                          <TableHead>中文名称</TableHead>
                          <TableHead>英文名称</TableHead>
                          <TableHead>省份</TableHead>
                          <TableHead>城市</TableHead>
                          <TableHead>联系人</TableHead>
                          <TableHead>手机</TableHead>
                          <TableHead>邮箱</TableHead>
                          <TableHead>验证状态</TableHead>
                        </TableRow>
                      </TableHeader>
                      <TableBody>
                        {companyData.slice(0, 10).map((company, index) => (
                          <TableRow key={index}>
                            <TableCell className="font-medium">{company.company_id}</TableCell>
                            <TableCell>{company.name}</TableCell>
                            <TableCell>{company.name_en}</TableCell>
                            <TableCell>{company.province}</TableCell>
                            <TableCell>{company.city}</TableCell>
                            <TableCell>{company.contact_person}</TableCell>
                            <TableCell>{company.mobile}</TableCell>
                            <TableCell>{company.email}</TableCell>
                            <TableCell>
                              <Badge variant={company.is_verified ? 'default' : 'secondary'}>
                                {company.is_verified ? '已验证' : '未验证'}
                              </Badge>
                            </TableCell>
                          </TableRow>
                        ))}
                      </TableBody>
                    </Table>
                  </div>
                  {companyData.length > 10 && (
                    <p className="text-sm text-muted-foreground mt-2">
                      显示前 10 条记录，共 {companyData.length} 条记录
                    </p>
                  )}
                </CardContent>
              </Card>
            </TabsContent>

            {validationErrors.length > 0 && (
              <TabsContent value="errors">
                <Card>
                  <CardHeader>
                    <CardTitle className="text-destructive">验证错误</CardTitle>
                    <CardDescription>
                      请修复以下错误后重新上传文件
                    </CardDescription>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-2">
                      {validationErrors.map((error, index) => (
                        <Alert key={index} variant="destructive">
                          <AlertCircle className="h-4 w-4" />
                          <AlertDescription>
                            <strong>第 {error.row} 行，{error.field}：</strong> {error.message}
                          </AlertDescription>
                        </Alert>
                      ))}
                    </div>
                  </CardContent>
                </Card>
              </TabsContent>
            )}

            {importResults && (
              <TabsContent value="results">
                <Card>
                  <CardHeader>
                    <CardTitle className="text-green-600">导入结果</CardTitle>
                    <CardDescription>
                      数据导入操作已完成
                    </CardDescription>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-4">
                      <div className="grid grid-cols-3 gap-4">
                        <div className="text-center p-4 bg-blue-50 rounded-lg">
                          <div className="text-2xl font-bold text-blue-600">{importResults.totalProcessed}</div>
                          <div className="text-sm text-blue-600">总处理数</div>
                        </div>
                        <div className="text-center p-4 bg-green-50 rounded-lg">
                          <div className="text-2xl font-bold text-green-600">{importResults.successCount}</div>
                          <div className="text-sm text-green-600">成功数</div>
                        </div>
                        <div className="text-center p-4 bg-red-50 rounded-lg">
                          <div className="text-2xl font-bold text-red-600">{importResults.errorCount}</div>
                          <div className="text-sm text-red-600">失败数</div>
                        </div>
                      </div>

                      {importResults.errors.length > 0 && (
                        <div className="space-y-2">
                          <h4 className="font-medium text-red-600">错误详情：</h4>
                          {importResults.errors.map((error, index) => (
                            <Alert key={index} variant="destructive">
                              <AlertCircle className="h-4 w-4" />
                              <AlertDescription>{error}</AlertDescription>
                            </Alert>
                          ))}
                        </div>
                      )}
                    </div>
                  </CardContent>
                </Card>
              </TabsContent>
            )}
          </Tabs>
        )}

        {/* Instructions */}
        <Alert>
          <AlertDescription>
            <strong>使用说明:</strong>
            <ul className="mt-2 space-y-1 text-sm">
              <li>• 点击"下载模板"获取标准的Excel模板文件，包含所有必需的字段</li>
              <li>• 支持Excel (.xlsx, .xls) 和CSV文件格式</li>
              <li>• 文件必须包含标题行，字段顺序必须与模板一致</li>
              <li>• <strong>company_id</strong>：公司ID，必须是有效的正整数</li>
              <li>• <strong>name/name_en</strong>：公司中英文名称，不能为空</li>
              <li>• <strong>mobile</strong>：手机号码，必须是有效的中国手机号码格式</li>
              <li>• <strong>email</strong>：邮箱地址，必须符合标准邮箱格式</li>
              <li>• <strong>homepage</strong>：网站地址，必须以http://或https://开头</li>
              <li>• <strong>is_verified</strong>：验证状态，1表示已验证，0表示未验证</li>
              <li>• 系统会自动验证数据格式，并同时更新seller_company和seller_company_lang表</li>
              <li>• 导入过程会根据company_id匹配现有记录进行更新</li>
            </ul>
          </AlertDescription>
        </Alert>
      </div>
    </div>
  )
}
