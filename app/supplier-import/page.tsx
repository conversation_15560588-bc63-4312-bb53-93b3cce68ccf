"use client"

import React, { useState, useRef } from "react"
import { 
  Upload, 
  FileText, 
  CheckCircle, 
  AlertCircle, 
  Download,
  Trash2,
  Eye,
  Save
} from "lucide-react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Alert, AlertDescription } from "@/components/ui/alert"
import { Badge } from "@/components/ui/badge"
import { Progress } from "@/components/ui/progress"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { Ta<PERSON>, <PERSON>bsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { useToast } from "@/hooks/use-toast"
import * as XLSX from 'xlsx'

interface SupplierData {
  id?: string
  name: string
  contact: string
  phone: string
  email: string
  address: string
  category: string
  status: 'active' | 'inactive'
}

interface ValidationError {
  row: number
  field: string
  message: string
}

export default function SupplierImportPage() {
  const { toast } = useToast()
  const fileInputRef = useRef<HTMLInputElement>(null)
  
  const [file, setFile] = useState<File | null>(null)
  const [supplierData, setSupplierData] = useState<SupplierData[]>([])
  const [validationErrors, setValidationErrors] = useState<ValidationError[]>([])
  const [isProcessing, setIsProcessing] = useState(false)
  const [importProgress, setImportProgress] = useState(0)
  const [importStatus, setImportStatus] = useState<'idle' | 'processing' | 'completed' | 'error'>('idle')

  const handleFileSelect = (event: React.ChangeEvent<HTMLInputElement>) => {
    const selectedFile = event.target.files?.[0]
    if (selectedFile) {
      setFile(selectedFile)
      processFile(selectedFile)
    }
  }

  const processFile = async (file: File) => {
    setIsProcessing(true)
    setImportProgress(0)
    
    try {
      const data = await file.arrayBuffer()
      const workbook = XLSX.read(data, { type: 'array' })
      const sheetName = workbook.SheetNames[0]
      const worksheet = workbook.Sheets[sheetName]
      const jsonData = XLSX.utils.sheet_to_json(worksheet, { header: 1 }) as any[][]

      if (jsonData.length < 2) {
        throw new Error('文件必须包含标题行和至少一行数据')
      }

      // 假设第一行是标题
      const headers = jsonData[0]
      const rows = jsonData.slice(1)

      // 映射数据到SupplierData格式
      const mappedData: SupplierData[] = rows.map((row, index) => ({
        name: row[0] || '',
        contact: row[1] || '',
        phone: row[2] || '',
        email: row[3] || '',
        address: row[4] || '',
        category: row[5] || '',
        status: (row[6] === '激活' || row[6] === 'active') ? 'active' : 'inactive'
      }))

      // 验证数据
      const errors = validateSupplierData(mappedData)
      setValidationErrors(errors)
      setSupplierData(mappedData)
      setImportProgress(100)
      
      if (errors.length === 0) {
        setImportStatus('completed')
        toast({
          title: "文件处理成功",
          description: `成功处理 ${mappedData.length} 条供应商记录`,
        })
      } else {
        setImportStatus('error')
        toast({
          title: "数据验证失败",
          description: `发现 ${errors.length} 个验证错误`,
          variant: "destructive",
        })
      }
    } catch (error) {
      setImportStatus('error')
      toast({
        title: "文件处理失败",
        description: error instanceof Error ? error.message : "未知错误",
        variant: "destructive",
      })
    } finally {
      setIsProcessing(false)
    }
  }

  const validateSupplierData = (data: SupplierData[]): ValidationError[] => {
    const errors: ValidationError[] = []
    
    data.forEach((supplier, index) => {
      const rowNumber = index + 2 // +2 because of header row and 0-based index
      
      if (!supplier.name.trim()) {
        errors.push({ row: rowNumber, field: '供应商名称', message: '供应商名称不能为空' })
      }
      
      if (!supplier.contact.trim()) {
        errors.push({ row: rowNumber, field: '联系人', message: '联系人不能为空' })
      }
      
      if (!supplier.phone.trim()) {
        errors.push({ row: rowNumber, field: '电话', message: '电话不能为空' })
      } else if (!/^1[3-9]\d{9}$/.test(supplier.phone.replace(/\s|-/g, ''))) {
        errors.push({ row: rowNumber, field: '电话', message: '电话格式不正确' })
      }
      
      if (supplier.email && !/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(supplier.email)) {
        errors.push({ row: rowNumber, field: '邮箱', message: '邮箱格式不正确' })
      }
      
      if (!supplier.category.trim()) {
        errors.push({ row: rowNumber, field: '类别', message: '供应商类别不能为空' })
      }
    })
    
    return errors
  }

  const handleImport = async () => {
    if (validationErrors.length > 0) {
      toast({
        title: "无法导入",
        description: "请先修复所有验证错误",
        variant: "destructive",
      })
      return
    }

    setIsProcessing(true)
    setImportProgress(0)
    
    try {
      // 模拟导入过程
      for (let i = 0; i <= 100; i += 10) {
        setImportProgress(i)
        await new Promise(resolve => setTimeout(resolve, 100))
      }
      
      // 这里可以添加实际的API调用来保存数据
      // await saveSupplierData(supplierData)
      
      toast({
        title: "导入成功",
        description: `成功导入 ${supplierData.length} 条供应商记录`,
      })
      
      // 清理状态
      setFile(null)
      setSupplierData([])
      setValidationErrors([])
      setImportStatus('idle')
      if (fileInputRef.current) {
        fileInputRef.current.value = ''
      }
    } catch (error) {
      toast({
        title: "导入失败",
        description: error instanceof Error ? error.message : "未知错误",
        variant: "destructive",
      })
    } finally {
      setIsProcessing(false)
      setImportProgress(0)
    }
  }

  const downloadTemplate = () => {
    const templateData = [
      ['供应商名称', '联系人', '电话', '邮箱', '地址', '类别', '状态'],
      ['示例供应商', '张三', '13800138000', '<EMAIL>', '北京市朝阳区', '原材料', '激活'],
      ['测试供应商', '李四', '13900139000', '<EMAIL>', '上海市浦东新区', '设备', '激活']
    ]
    
    const ws = XLSX.utils.aoa_to_sheet(templateData)
    const wb = XLSX.utils.book_new()
    XLSX.utils.book_append_sheet(wb, ws, '供应商模板')
    XLSX.writeFile(wb, '供应商导入模板.xlsx')
  }

  const clearData = () => {
    setFile(null)
    setSupplierData([])
    setValidationErrors([])
    setImportStatus('idle')
    setImportProgress(0)
    if (fileInputRef.current) {
      fileInputRef.current.value = ''
    }
  }

  return (
    <div className="container mx-auto px-4 py-8">
      <div className="max-w-6xl mx-auto space-y-6">
        {/* Header */}
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold text-gray-900">Supplier Import</h1>
            <p className="text-muted-foreground mt-1">Import and manage supplier data with validation and preview</p>
          </div>
          <div className="flex items-center gap-2">
            <Button variant="outline" onClick={downloadTemplate}>
              <Download className="w-4 h-4 mr-2" />
              下载模板
            </Button>
          </div>
        </div>

        {/* File Upload Section */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Upload className="h-5 w-5" />
              文件上传
            </CardTitle>
            <CardDescription>
              支持 Excel (.xlsx, .xls) 和 CSV 文件格式。请确保文件包含标题行。
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div className="flex items-center gap-4">
                <input
                  ref={fileInputRef}
                  type="file"
                  accept=".xlsx,.xls,.csv"
                  onChange={handleFileSelect}
                  className="hidden"
                />
                <Button 
                  onClick={() => fileInputRef.current?.click()}
                  disabled={isProcessing}
                >
                  <Upload className="w-4 h-4 mr-2" />
                  选择文件
                </Button>
                {file && (
                  <div className="flex items-center gap-2">
                    <FileText className="h-4 w-4 text-muted-foreground" />
                    <span className="text-sm">{file.name}</span>
                    <Button variant="ghost" size="sm" onClick={clearData}>
                      <Trash2 className="h-3 w-3" />
                    </Button>
                  </div>
                )}
              </div>
              
              {isProcessing && (
                <div className="space-y-2">
                  <div className="flex items-center justify-between text-sm">
                    <span>处理进度</span>
                    <span>{importProgress}%</span>
                  </div>
                  <Progress value={importProgress} />
                </div>
              )}
            </div>
          </CardContent>
        </Card>

        {/* Status and Results */}
        {supplierData.length > 0 && (
          <Tabs defaultValue="preview" className="space-y-4">
            <TabsList>
              <TabsTrigger value="preview" className="flex items-center gap-2">
                <Eye className="h-4 w-4" />
                数据预览 ({supplierData.length})
              </TabsTrigger>
              {validationErrors.length > 0 && (
                <TabsTrigger value="errors" className="flex items-center gap-2">
                  <AlertCircle className="h-4 w-4" />
                  验证错误 ({validationErrors.length})
                </TabsTrigger>
              )}
            </TabsList>

            <TabsContent value="preview">
              <Card>
                <CardHeader className="flex flex-row items-center justify-between">
                  <div>
                    <CardTitle>数据预览</CardTitle>
                    <CardDescription>
                      预览即将导入的供应商数据
                    </CardDescription>
                  </div>
                  <div className="flex items-center gap-2">
                    <Badge variant={validationErrors.length === 0 ? "default" : "destructive"}>
                      {validationErrors.length === 0 ? (
                        <><CheckCircle className="h-3 w-3 mr-1" />验证通过</>
                      ) : (
                        <><AlertCircle className="h-3 w-3 mr-1" />{validationErrors.length} 个错误</>
                      )}
                    </Badge>
                    <Button 
                      onClick={handleImport}
                      disabled={validationErrors.length > 0 || isProcessing}
                    >
                      <Save className="w-4 h-4 mr-2" />
                      确认导入
                    </Button>
                  </div>
                </CardHeader>
                <CardContent>
                  <div className="rounded-md border">
                    <Table>
                      <TableHeader>
                        <TableRow>
                          <TableHead>供应商名称</TableHead>
                          <TableHead>联系人</TableHead>
                          <TableHead>电话</TableHead>
                          <TableHead>邮箱</TableHead>
                          <TableHead>地址</TableHead>
                          <TableHead>类别</TableHead>
                          <TableHead>状态</TableHead>
                        </TableRow>
                      </TableHeader>
                      <TableBody>
                        {supplierData.slice(0, 10).map((supplier, index) => (
                          <TableRow key={index}>
                            <TableCell className="font-medium">{supplier.name}</TableCell>
                            <TableCell>{supplier.contact}</TableCell>
                            <TableCell>{supplier.phone}</TableCell>
                            <TableCell>{supplier.email}</TableCell>
                            <TableCell>{supplier.address}</TableCell>
                            <TableCell>{supplier.category}</TableCell>
                            <TableCell>
                              <Badge variant={supplier.status === 'active' ? 'default' : 'secondary'}>
                                {supplier.status === 'active' ? '激活' : '未激活'}
                              </Badge>
                            </TableCell>
                          </TableRow>
                        ))}
                      </TableBody>
                    </Table>
                  </div>
                  {supplierData.length > 10 && (
                    <p className="text-sm text-muted-foreground mt-2">
                      显示前 10 条记录，共 {supplierData.length} 条记录
                    </p>
                  )}
                </CardContent>
              </Card>
            </TabsContent>

            {validationErrors.length > 0 && (
              <TabsContent value="errors">
                <Card>
                  <CardHeader>
                    <CardTitle className="text-destructive">验证错误</CardTitle>
                    <CardDescription>
                      请修复以下错误后重新上传文件
                    </CardDescription>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-2">
                      {validationErrors.map((error, index) => (
                        <Alert key={index} variant="destructive">
                          <AlertCircle className="h-4 w-4" />
                          <AlertDescription>
                            <strong>第 {error.row} 行，{error.field}：</strong> {error.message}
                          </AlertDescription>
                        </Alert>
                      ))}
                    </div>
                  </CardContent>
                </Card>
              </TabsContent>
            )}
          </Tabs>
        )}

        {/* Instructions */}
        <Alert>
          <AlertDescription>
            <strong>使用说明:</strong>
            <ul className="mt-2 space-y-1 text-sm">
              <li>• 点击"下载模板"获取标准的Excel模板文件</li>
              <li>• 支持Excel (.xlsx, .xls) 和CSV文件格式</li>
              <li>• 文件必须包含标题行：供应商名称、联系人、电话、邮箱、地址、类别、状态</li>
              <li>• 电话号码必须是有效的中国手机号码格式</li>
              <li>• 邮箱地址必须符合标准邮箱格式</li>
              <li>• 状态字段支持"激活"/"active"和"未激活"/"inactive"</li>
              <li>• 系统会自动验证数据格式，只有通过验证的数据才能导入</li>
            </ul>
          </AlertDescription>
        </Alert>
      </div>
    </div>
  )
}
